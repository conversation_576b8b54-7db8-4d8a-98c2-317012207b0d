'use client';
import { useFormContext } from 'react-hook-form';
import { useRouter } from 'next/navigation';

import { GradientBorderCard } from '@/components/cards';
import { CreateEventLayout } from '@/components/layouts';
import { Button } from '@/components/ui';
import { type CreateEventFormType, EVENT_TYPE_CARDS } from '@/lib';

export default function ChooseEventVisibility() {
  const { watch, setValue } = useFormContext<CreateEventFormType>();
  const router = useRouter();
  const eventType = watch('eventType');

  return (
    <CreateEventLayout
      title='What type of event are you hosting?'
      subTitle='Choose whether your event is public or private.'
      footer={
        <Button
          label='Continue'
          className='m-4'
          disabled={!eventType}
          onClick={ () => router.push('/events/create/add-details')}
        />
      }
    >
      {EVENT_TYPE_CARDS.map((card) => (
        <GradientBorderCard
          key={card.id}
          isSelected={eventType === card.id}
          onClick={() => setValue('eventType', card.id)}
          icon={card.icon}
          title={card.title}
          description={card.description}
        />
      ))}
    </CreateEventLayout>
  );
}
