'use client';

import { useRouter } from 'next/navigation';
import { useCallback } from 'react';
import { useFormContext } from 'react-hook-form';
import { type Point, type LocationType } from '@/types';

import LocationInput from '@/components/inputs/location-autocomplete';
import { CreateEventLayout } from '@/components/layouts/create-event-layout';
import { Button } from '@/components/ui';
import { type CreateEventFormType, useFieldBlurAndFilled } from '@/lib';

export default function AddAddress() {
  const { watch, setValue } = useFormContext<CreateEventFormType>();
  const { fieldStates } = useFieldBlurAndFilled<CreateEventFormType>([
    'location',
  ]);
  const router = useRouter();

  const onSelectLocation = useCallback(
    async (
      location: Omit<LocationType, 'coordinates'>,
      locationCoordinate: Point
    ) => {
      const locationObject = {
        ...location,
        coordinates: { ...locationCoordinate },
      };
      setValue('location', locationObject);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  return (
    <CreateEventLayout
      title='Add event address'
      subTitle='Where will your event be taking place?'
      footer={
        <Button
          className='m-4'
          disabled={!fieldStates.location.isValid}
          onClick={() => router.push('/events/create/choose-ticket-type')}
        >
          Continue
        </Button>
      }
    >
      <div className='gap-4'>
        <LocationInput
          onSelectLocation={onSelectLocation}
          defaultValue={watch('location')}
        />
      </div>
    </CreateEventLayout>
  );
}
