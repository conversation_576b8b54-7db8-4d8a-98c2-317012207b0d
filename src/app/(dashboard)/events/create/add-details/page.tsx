'use client';
import { useCallback, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { useRouter } from 'next/navigation';
import { type IEventCategories, useGetEventCategories } from '@/api/events';
import { ConfirmationDialog } from '@/components/dialogs';
import { CollaboratorCard } from '@/components/events';
import { CreateEventLayout } from '@/components/layouts';
import { ControlledSelect, P, PillOption, Spinner } from '@/components/ui';
import {
  Button,
  ControlledInput,
  Modal,
  colors,
} from '@/components/ui';
import { type CreateEventFormType, useFieldBlurAndFilled } from '@/lib';
import TIMEZONES_JSON from '@/lib/timezones.json';
import { formatDateTime } from '@/lib';
import DatePicker from 'react-datepicker';

const timezones = TIMEZONES_JSON.map(({ text, value }) => ({
  label: text,
  value,
}));

export default function AddDetails() {
  const { watch, control, setValue } = useFormContext<CreateEventFormType>();
  const router = useRouter();

  const [confirmVisible, setConfirmVisible] = useState(false);
  const [artistToDelete, setArtistToDelete] = useState<string | null>(null);

  const { data: categories, isLoading: categoriesLoading } =
    useGetEventCategories();

  const isDark =
    window.matchMedia &&
    window.matchMedia('(prefers-color-scheme: dark)').matches;

  const selectedCategory = watch('category');
  const timezone = watch('timezone');
  const artists = watch('collaborators') || [];
  const startDatetime = watch('startDatetime');
  const endDatetime = watch('endDatetime');

  const startDatetimeLabel = startDatetime
    ? formatDateTime(startDatetime)
    : 'Start date and time';

  const endDatetimeLabel = endDatetime
    ? formatDateTime(endDatetime)
    : 'End date and time';

  const isStartDateValid = (date: Date) => date >= new Date();

  const isEndDateValid = (startDate: Date, endDate: Date) =>
    endDate >= startDate;

  const handleStartDateChange = (date: Date) => {
    if (!isStartDateValid(date)) {
      alert('Start date cannot be in the past.');
      return;
    }
    setValue('startDatetime', date);
  };

  const handleEndDateChange = (date: Date) => {
    const start = new Date(watch('startDatetime'));
    if (!start || !isEndDateValid(start, date)) {
      alert('End date cannot be before the start date.');
      return;
    }
    setValue('endDatetime', date);
  };


  const { fieldStates } =
    useFieldBlurAndFilled<CreateEventFormType>([
      'title',
      'description',
      'timezone',
      'category',
      'startDatetime',
      'endDatetime',
      'collaborators',
    ]);

  const selectCategory = useCallback(
    (category: IEventCategories) => {
      const currentCategory = watch('category');
      if (currentCategory === category.id) {
        setValue('category', '');
      } else {
        setValue('category', category.id);
      }
    },
    [setValue, watch]
  );

  const confirmCategory = useCallback(() => {
    setCategoryModalOpen(false);
  }, []);

  const categoryLabel = categories?.find(
    (c) => c.id === selectedCategory
  )?.category;

  const [categoryModalOpen, setCategoryModalOpen] = useState(false);

  return (
    <CreateEventLayout
      title='Tell us about your event'
      subTitle='Add details to make your event stand out.'
      footer={
        <Button
          label='Continue'
          className='mx-4'
          disabled={
            !fieldStates.title.isValid ||
            !fieldStates.description.isValidOptional ||
            !fieldStates.collaborators.isValidOptional ||
            !fieldStates.startDatetime.isValid ||
            !fieldStates.endDatetime.isValid ||
            !fieldStates.timezone.isValid ||
            !fieldStates.category.isValid
          }
          onClick={() => router.push('/events/create/add-banner')}
        />
      }
    >
          <ControlledInput
            name='title'
            label='Event title'
            control={control}
          />
          <ControlledInput
            name='description'
            multiline
            label='Describe your event'
            control={control}
          />

          <DatePicker
            selected={startDatetime}
            onChange={(date) => handleStartDateChange(date as Date)}
            showTimeSelect
            minDate={new Date()}
            dateFormat="MMMM d, yyyy h:mm aa"
            customInput={
          <button
            className='h-12 flex items-center justify-between rounded-md border border-border-subtle-light p-3 dark:border-border-subtle-dark cursor-pointer'
          >
            <p
              className={
                startDatetime
                  ? 'dark:text-neutral-100'
                  : 'text-neutral-400 dark:text-neutral-500'
              }
            >
              {startDatetimeLabel}
            </p>
          </button>
          }
          />

          <DatePicker
            selected={endDatetime}
            onChange={(date) => handleEndDateChange(date as Date)}
            showTimeSelect
            minDate={startDatetime}
            dateFormat="MMMM d, yyyy h:mm aa"
            customInput={
          <button
            className='h-12 flex items-center justify-between rounded-md border border-border-subtle-light p-3 dark:border-border-subtle-dark cursor-pointer'
          >
            <p
              className={
                endDatetime
                  ? 'dark:text-neutral-100'
                  : 'text-neutral-400 dark:text-neutral-500'
              }
            >
              {endDatetimeLabel}
            </p>
          </button>
          }
          />

          <ControlledSelect
            control={control}
            name='timezone'
            placeholder='Timezone'
            options={timezones}
          />

          <a
            onClick={() => setCategoryModalOpen(true)}
            className='h-12 flex items-center justify-between rounded-md border border-border-subtle-light p-3 dark:border-border-subtle-dark cursor-pointer'
          >
            <div className='flex items-center gap-2 flex-row'>
              <p
                className={
                  selectedCategory
                    ? 'dark:text-neutral-100'
                    : 'text-neutral-400 dark:text-neutral-500'
                }
              >
                {categoryLabel || 'Category'}
              </p>
            </div>
            <span className='icon-chevron-down' />
          </a>

          {artists.length < 1 && (
            <Button
              label='Add artiste +'
              variant='secondary'
              className='w-[154px] py-2'
              size='sm'
              onClick={() => router.push('/events/create/add-collaborators')}
            />
          )}

        {artists.length > 0 && (
          <div className='mt-4 gap-4'>
            <P>Lineup</P>
            <div
              style={{ overflowX: 'auto', paddingBottom: 8 }}
              className='flex-row items-center gap-2'
            >
              {artists.map((artist) => (
                <CollaboratorCard
                  key={artist.id}
                  artist={artist}
                  onRemove={() => {
                    setArtistToDelete(artist.id);
                    setConfirmVisible(true);
                  }}
                />
              ))}
              <div className='h-[92px] w-20 flex items-center justify-center'>
                <div
                  className='size-12 flex items-center justify-center rounded-full bg-accent-subtle-light dark:bg-accent-subtle-dark cursor-pointer'
                  onClick={() => router.push('/events/create/add-collaborators')}
                  aria-label='Add collaborator'
                >
                  <span
                    className='icon-add'
                    style={{
                      fontSize: 32,
                      color: isDark ? colors.brand[40] : colors.brand[70],
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
        )}

      <ConfirmationDialog
        visible={confirmVisible}
        message='Are you sure you want to delete this artist?'
        onCancel={() => {
          setConfirmVisible(false);
          setArtistToDelete(null);
        }}
        onConfirm={() => {
          if (artistToDelete !== null) {
            setTimeout(() => {
              setValue(
                'collaborators',
                artists.filter((a) => a.id !== artistToDelete)
              );
            }, 0);
          }
          setConfirmVisible(false);
          setArtistToDelete(null);
        }}
      />

      <Modal
        isOpen={categoryModalOpen}
        onClose={() => setCategoryModalOpen(false)}
      >
        {categoriesLoading ? (
        
          <Spinner />
        ) : (
          <div className='min-h-20 flex-1 gap-4 px-4 pb-4'>
            <div className='mb-4 flex items-center justify-start gap-2'>
              <button
                onClick={() => setCategoryModalOpen(false)}
                aria-label='Close category modal'
                type='button'
                className='icon-close'
              />
              <p className='text-lg font-bold dark:text-neutral-100'>
                Select event category
              </p>
            </div>

            <div className='flex flex-wrap gap-2'>
              {categories?.map((category) => (
                <PillOption
                  key={category.id}
                  option={category.category}
                  selected={selectedCategory === category.id}
                  onPress={() => selectCategory(category)}
                />
              ))}
            </div>

            <div className='mt-auto pb-4'>
              <Button
                label='Confirm'
                disabled={!selectedCategory}
                onClick={confirmCategory}
              />
            </div>
          </div>
        )}
      </Modal>
    </CreateEventLayout>
  );
}
