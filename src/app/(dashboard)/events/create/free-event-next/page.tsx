import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useFormContext } from 'react-hook-form';

import { CreateEventLayout } from '@/components/layouts/create-event-layout';
import { Button, ControlledInput, View } from '@/components/ui';
import { type CreateEventFormType } from '@/lib/constants';

export default function FreeEventDetails() {
  const { control, setValue } = useFormContext<CreateEventFormType>();

  const { fieldStates, handleFieldBlur, handleFieldUnBlur } =
    useFieldBlurAndFilled<CreateEventFormType>(['maxAttendees']);

  const navigate = useNavigate();

  return (
    <CreateEventLayout
      title='Set up free event'
      subTitle='Choose between requiring attendees to register for the event or just uploading your event.'
      footer={
        <Button
          data-testid='go-to-add-banner-page-button'
          label='Continue'
          className='mx-4'
          disabled={!fieldStates.maxAttendees.isValidOptional}
          onClick={() => navigate('/events/create/summary')}
        />
      }
    >
      <div className='gap-4'>
        <ControlledInput
          name='maxAttendees'
          label='Max. number of attendees'
          handleFieldBlur={() => handleFieldBlur('maxAttendees')}
          handleFieldUnBlur={() => handleFieldUnBlur('maxAttendees')}
          control={control}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            setValue('maxAttendees', Number(e.target.value))
          }
          type='number'
          inputMode='numeric'
        />
      </div>
    </CreateEventLayout>
  );
}
