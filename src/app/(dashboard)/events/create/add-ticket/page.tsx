import { useRouter } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';
import { CreateEventLayout } from '@/components/layouts';
import {
  Button,
  ControlledInput,
  CostSelector,
  FeatureToggle,
  P,
} from '@/components/ui';
import {
  type CreateEventFormType,
  useCreateEventForm,
  useFieldBlurAndFilled,
} from '@/lib';
import { formatDate, formatDateTime } from '@/lib';
import { isStartDateValid, isEndDateValid } from '@/lib';
import DatePicker from "react-datepicker";

export default function AddTicket() {
  const { watch, control, setValue, getValues, subscribe } =
    useFormContext<CreateEventFormType>();
  const { defaultTicket } = useCreateEventForm();
  const router = useRouter();
  const { fieldStates } =
    useFieldBlurAndFilled<CreateEventFormType>([
      'tickets.0.name',
      'tickets.0.price',
      'tickets.0.quantity',
      'tickets.0.presaleName',
      'tickets.0.presalePrice',
      'tickets.0.presaleQuantity',
      'tickets.0.presaleStartDatetime',
      'tickets.0.presaleEndDatetime',
      'tickets.0.purchaseLimit',
      'tickets.0.description',
      'tickets.0.startDatetime',
      'tickets.0.endDatetime',
    ]);
  const { append, update } = useFieldArray({
    control,
    name: 'tickets',
  });

  // can enable all features
  const name = watch(`tickets.0.name`);
  const price = watch(`tickets.0.price`);
  const quantity = watch(`tickets.0.quantity`);
  const canEnable = name && price && quantity;

  // presale toggle and modal
  const [hasPresale, setHasPresale] = useState(watch(`tickets.0.hasPresale`));
  function enablePresale() {
    setHasPresale(true);
    setValue(`tickets.0.hasPresale`, true);
    setValue(`tickets.0.presalePrice`, 0);
    setValue(`tickets.0.presaleQuantity`, 0);
    setValue('tickets.0.presaleName', getValues('tickets.0.name') + ' - ');
    setValue(`tickets.0.presaleDescription`, '');
  }

  function disablePresale() {
    if (watch(`tickets.0.hasPresale`)) {
      setHasPresale(false);
      setValue(`tickets.0.hasPresale`, false, { shouldValidate: true });
    }
  }

  const togglePresale = (checked: boolean) => {
    if (canEnable && checked) {
      enablePresale();
    } else {
      disablePresale();
    }
  };

  const presaleNameTouched = useRef(false);

  useEffect(() => {
    const unsub = subscribe({
      name: 'tickets.0.name',
      callback: ({ values }) => {
        const ticket = values.tickets?.[0];
        if (ticket?.hasPresale) {
          const name = ticket.name;
          const currentPresaleName =
            'presaleName' in ticket ? ticket.presaleName : undefined;
          const generatedPresaleName = name ? `${name} - ` : '';

          if (
            name &&
            !presaleNameTouched.current &&
            currentPresaleName !== generatedPresaleName
          ) {
            setValue('tickets.0.presaleName', generatedPresaleName);
          }
        }
      },
    });

    return () => unsub();
  }, [subscribe, setValue]);

  const eventEnd = watch('endDatetime');

  // presale datetimes
  const [openPresaleStartDatetime, setOpenPresaleStartDatetime] =
    useState(false);
  const [openPresaleEndDatetime, setOpenPresaleEndDatetime] = useState(false);

  const presaleStartDatetime = watch(`tickets.0.presaleStartDatetime`);

  const presaleStartDatetimeLabel = presaleStartDatetime
    ? formatDateTime(presaleStartDatetime)
    : 'Start date and time';

  const presaleEndDatetime = watch(`tickets.0.presaleEndDatetime`);

  const presaleEndDatetimeLabel = presaleEndDatetime
    ? formatDateTime(presaleEndDatetime)
    : 'End date and time';

  const handlePresaleStartDateConfirm = (date: Date) => {
    if (!isStartDateValid(date)) {
      alert('Start date cannot be in the past.');
      return;
    }
    setValue(`tickets.0.presaleStartDatetime`, date);
    setOpenPresaleStartDatetime(false);
  };

  const handlePresaleEndDateConfirm = (date: Date) => {
    const start = watch(`tickets.0.presaleStartDatetime`);
    if (!start || !isEndDateValid(new Date(start), date)) {
      alert('End date cannot be before the start date.');
      return;
    }
    if (eventEnd && date > new Date(eventEnd)) {
      alert('Presale ticket end date cannot be after event end date.');
      return;
    }
    setValue(`tickets.0.presaleEndDatetime`, date);
    setOpenPresaleEndDatetime(false);
  };

  //timeline
  const [hasTimeline, setHasTimeline] = useState(
    watch(`tickets.0.hasTimeline`)
  );

  const toggleTimeline = (checked: boolean) => {
    if (canEnable && checked) {
      setHasTimeline(true);
      setValue(`tickets.0.hasTimeline`, true);
    } else {
      setHasTimeline(false);
      setValue(`tickets.0.hasTimeline`, false);
    }
  };

  // timeline datetimes
  const [openStartDatetime, setOpenStartDatetime] = useState(false);
  const [openEndDatetime, setOpenEndDatetime] = useState(false);

  const startDatetime = watch(`tickets.0.startDatetime`);

  const startDatetimeLabel = startDatetime
    ? formatDateTime(startDatetime)
    : 'Start date and time';

  const endDatetime = watch(`tickets.0.endDatetime`);

  const endDatetimeLabel = endDatetime
    ? formatDateTime(endDatetime)
    : 'End date and time';

  const handleStartDateConfirm = (date: Date) => {
    if (!isStartDateValid(date)) {
      alert('Start date cannot be in the past.');
      return;
    }
    setValue(`tickets.0.startDatetime`, date);
    setOpenStartDatetime(false);
  };

  const handleEndDateConfirm = (date: Date) => {
    const start = watch('tickets.0.startDatetime');

    if (!start || !isEndDateValid(new Date(start), date)) {
      alert('End date cannot be before the start date.');
      return;
    }

    if (eventEnd && date > new Date(eventEnd)) {
      alert('Ticket end date cannot be after event end date.');
      return;
    }

    setValue('tickets.0.endDatetime', date);
    setOpenEndDatetime(false);
  };

  //Purchase limit
  const [localPurchaseLimit, setLocalPurchaseLimit] = useState(10);
  const [hasPurchaseLimit, setHasPurchaseLimit] = useState(
    watch(`tickets.0.hasPurchaseLimit`)
  );

  const togglePurchaseLimit = (checked: boolean) => {
    if (canEnable && checked) {
      setHasPurchaseLimit(true);
      setValue(`tickets.0.hasPurchaseLimit`, true);
    } else {
      setHasPurchaseLimit(false);
      setValue(`tickets.0.hasPurchaseLimit`, false);
    }
  };

  const handleAddTicket = () => {
    append(watch('tickets.0'));

    // Create a proper default ticket that matches the schema requirements
    const currentTicket = watch('tickets.0');
    const resetTicket = currentTicket?.hasPresale
      ? {
          ...defaultTicket,
          hasPresale: true as const,
          presalePrice: 0,
          presaleName: '',
          presaleQuantity: 0,
          presaleStartDatetime: new Date(),
          presaleEndDatetime: new Date(),
          presaleDescription: '',
          presalePurchaseLimit: 0,
        }
      : {
          ...defaultTicket,
          hasPresale: false as const,
        };

    update(0, resetTicket);
    router.push('/events/create/tickets');
  };

  // Presale purchase limit
  const [localPresalePurchaseLimit, setLocalPresalePurchaseLimit] = useState(
    watch(`tickets.0.presalePurchaseLimit`) || 10
  );

  return (
    <CreateEventLayout
      title='Set up tickets'
      subTitle='You can create multiple ticket types with varying prices.'
      footer={
        <Button
          label='Continue'
          className='m-4'
          disabled={
            !fieldStates['tickets.0.price'].isValid ||
            !watch(`tickets.0.name`) ||
            !watch(`tickets.0.price`) ||
            !watch(`tickets.0.quantity`)
          }
          onClick={() => {
            handleAddTicket();
          }}
        />
      }
    >
      <div className='space-4'>
        <ControlledInput
          name={`tickets.0.name`}
          label='Ticket name e.g General admission, VIP'
          control={control}
        />
        <CostSelector
          control={control}
          setValue={setValue}
          name={`tickets.0.price`}
          costOptions={[5000, 10000, 20000, 50000]}
        />
        <ControlledInput
          name={`tickets.0.quantity`}
          label='Quantity available'
          control={control}
        />
        <ControlledInput
          name={`tickets.0.description`}
          label='Ticket description (optional)'
          control={control}
        />
        <FeatureToggle
          title='Enable purchase limit'
          subtitle='Limit ticket per order, default uses a maximum of 10'
          checked={hasPurchaseLimit}
          onChange={togglePurchaseLimit}
          {...(hasPurchaseLimit && {
            editText: `Maximum of ${localPurchaseLimit} ticket(s)`,
          })}
          accessibilityLabel='Enable purchase limit'
        />
        <FeatureToggle
          title='Include ticket sale timeline'
          subtitle='Set specific period for ticket sale, default uses event date and time'
          checked={hasTimeline}
          onChange={toggleTimeline}
          {...(watch(`tickets.0.hasTimeline`) &&
            startDatetime &&
            endDatetime && {
              editText: `${formatDate(startDatetime)} - ${formatDate(
                endDatetime
              )}`,
            })}
          accessibilityLabel='Include ticket sale timeline'
        />
        <div className='gap-4 px-4 pb-6'>
          <P className='text-sm text-neutral-500 dark:text-neutral-400'>
            Set specific period for this ticket type sale
          </P>
          <button
            className='h-12 flex-row items-center justify-between rounded-md border border-border-subtle-light p-3 dark:border-border-subtle-dark'
            onClick={() => setOpenStartDatetime(true)}
          >
            <p
              className={
                startDatetime
                  ? 'dark:text-neutral-100'
                  : 'text-neutral-400 dark:text-neutral-500'
              }
            >
              {startDatetimeLabel}
            </p>
          </button>
          <DatePicker
            selected={startDatetime}
            onChange={(date) => handleStartDateConfirm(date as Date)}
          showTimeSelect
          minDate={new Date()}
          dateFormat="MMMM d, yyyy h:mm aa"
          customInput={
            <button
              type="button"
              className="h-12 w-full flex-row items-center justify-between rounded-md border border-border-subtle-light p-3 dark:border-border-subtle-dark"
            >
              <p
                className={
                  startDatetime
                    ? "dark:text-neutral-100"
                    : "text-neutral-400 dark:text-neutral-500"
                }
              >
                {startDatetimeLabel}
              </p>
            </button>
          }
          />

          <DatePicker
            onChange={(date) => handleEndDateConfirm(date as Date)}
          selected={endDatetime}
          showTimeSelect
          minDate={startDatetime}
          dateFormat="MMMM d, yyyy h:mm aa"
          customInput={
            <button
              type="button"
              className="h-12 w-full flex-row items-center justify-between rounded-md border border-border-subtle-light p-3 dark:border-border-subtle-dark"
            >
              <p
                className={
                  endDatetime
                    ? "dark:text-neutral-100"
                    : "text-neutral-400 dark:text-neutral-500"
                }
              >
                {endDatetimeLabel}
              </p>
            </button>
          }
            disabled={!startDatetime}
          />
        </div>
        <FeatureToggle
          title='Offer ticket presale'
          checked={hasPresale}
          onChange={togglePresale}
          {...(watch(`tickets.0.hasPresale`) && {
            editText: 'Edit presale ticket',
          })}
          accessibilityLabel='Offer ticket presale'
        />
        {hasPresale && (
        <div className='gap-4 px-4'>
          <ControlledInput
            name={`tickets.0.presaleName`}
            onChange={(e) => {
              presaleNameTouched.current = true;
              setValue('tickets.0.presaleName', e.target.value);
            }}
            label='Ticket name e.g General admission, VIP'
            control={control}
          />
          <ControlledInput
            name={`tickets.0.presalePrice`}
            label='Price'
            control={control}
          />

          <button
            className='h-12 flex-row items-center justify-between rounded-md border border-border-subtle-light p-3 dark:border-border-subtle-dark'
            onClick={() => setOpenPresaleStartDatetime(true)}
          >
            <p
              className={
                presaleStartDatetime
                  ? 'dark:text-neutral-100'
                  : 'text-neutral-400 dark:text-neutral-500'
              }
            >
              {presaleStartDatetimeLabel}
            </p>
          </button>
          <DatePicker
            selected={presaleStartDatetime}
            onChange={(date) => handlePresaleStartDateConfirm(date as Date)}
            showTimeSelect
            minDate={new Date()}
            dateFormat="MMMM d, yyyy h:mm aa"
            customInput={
              <button
                type="button"
                className="h-12 w-full flex-row items-center justify-between rounded-md border border-border-subtle-light p-3 dark:border-border-subtle-dark"
              >
                <p
                  className={
                    presaleStartDatetime
                      ? "dark:text-neutral-100"
                      : "text-neutral-400 dark:text-neutral-500"
                  }
                >
                  {presaleStartDatetime
                    ? presaleStartDatetime.toLocaleString()
                    : "Select a date"}
                </p>
              </button>
            }
          />
          <DatePicker
            selected={presaleEndDatetime}
            onChange={(date) => handlePresaleEndDateConfirm(date as Date)}
            showTimeSelect
            minDate={presaleStartDatetime}
            dateFormat="MMMM d, yyyy h:mm aa"
            customInput={
              <button
                type="button"
                className={`h-12 w-full flex-row items-center justify-between rounded-md border p-3 ${
                  presaleStartDatetime
                    ? 'border-border-subtle-light dark:border-border-subtle-dark'
                    : 'border-neutral-200 opacity-50 dark:border-neutral-700'
                }`}
                disabled={!presaleStartDatetime}
              ><p
                    className={
                      presaleEndDatetime
                        ? 'dark:text-neutral-100'
                        : 'text-neutral-400 dark:text-neutral-500'
                    }
                  >
                    {presaleEndDatetimeLabel}
                  </p></button>
                }
            />

          <ControlledInput
            name={`tickets.0.presaleQuantity`}
            label='Quantity available'
            control={control}
          />
          <ControlledInput
            name={`tickets.0.presaleDescription`}
            label='Ticket description (optional)'
            control={control}
          />
          <div className='flex-row items-center justify-between rounded-md bg-bg-subtle-light p-3 dark:bg-bg-subtle-dark'>
            <p className='text-base font-medium dark:text-neutral-100'>
              Max quantity per order
            </p>

            <div className='flex-row items-center rounded-full border border-gray-300 px-2 py-1 dark:border-gray-600'>
              <button
                onClick={() => {
                  if (localPresalePurchaseLimit > 1) {
                    setLocalPresalePurchaseLimit(
                      localPresalePurchaseLimit - 1
                    );
                  }
                }}
              >
                <p className='px-2 text-lg font-bold text-accent-moderate dark:text-accent-moderate'>
                  -
                </p>
              </button>

              <p className='px-2 text-base font-semibold dark:text-neutral-100'>
                {localPresalePurchaseLimit}
              </p>

              <button
                onClick={() => {
                  if (localPresalePurchaseLimit < 10) {
                    setLocalPresalePurchaseLimit(
                      localPresalePurchaseLimit + 1
                    );
                  }
                }}
              >
                <p className='px-2 text-lg font-bold text-accent-moderate dark:text-accent-moderate'>
                  +
                </p>
              </button>
            </div>
          </div>
        </div>)}

        <div className='min-h-10 gap-4 px-4 pb-6'>
          <div className='flex-row items-center justify-start gap-2'>
            <p className='text-lg font-bold dark:text-neutral-100'>
              Enable purchase limit
            </p>
          </div>
          <P className='text-sm text-neutral-500 dark:text-neutral-400'>
            Set the maximum number of this ticket type a single buyer can
            include in their order
          </P>
          <div className='flex-row items-center justify-between'>
            <p className='text-base font-medium dark:text-neutral-100'>
              Number of allowed purchase
            </p>

            <div className='flex-row items-center rounded-full border border-gray-300 px-2 py-1 dark:border-gray-600'>
              <button
                onClick={() => {
                  if (localPurchaseLimit > 1) {
                    setLocalPurchaseLimit(localPurchaseLimit - 1);
                  }
                }}
              >
                <p className='px-2 text-lg font-bold text-accent-moderate dark:text-accent-moderate'>
                  -
                </p>
              </button>

              <p className='px-2 text-base font-semibold dark:text-neutral-100'>
                {localPurchaseLimit}
              </p>

              <button
                onClick={() => {
                  if (localPurchaseLimit < 10) {
                    setLocalPurchaseLimit(localPurchaseLimit + 1);
                  }
                }}
              >
                <p className='px-2 text-lg font-bold text-accent-moderate dark:text-accent-moderate'>
                  +
                </p>
              </button>
            </div>
          </div>
        </div>
        </div>
    </CreateEventLayout>
  );
}
