'use client';
import React, { useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { useRouter } from 'next/navigation';

import { GradientBorderCard } from '@/components/cards/gradient-border-card';
import { CreateEventLayout } from '@/components/layouts/create-event-layout';
import {
  Button,
  ControlledInput,
  H2,
  Modal,
  P,
} from '@/components/ui';
import colors from '@/components/ui/colors';
import {
  type CreateEventFormType,
  EVENT_FORMAT_CARDS,
  useFieldBlurAndFilled,
} from '@/lib';
import { IoChevronBack } from 'react-icons/io5';
import { FaGlobe } from 'react-icons/fa';

export default function ChooseEventFormat() {
  const { watch, setValue, control } = useFormContext<CreateEventFormType>();
  const { fieldStates } = useFieldBlurAndFilled<CreateEventFormType>([
    'eventFormat',
    'onlineUrl',
    'passcode',
    'location',
  ]);
  const router = useRouter();

  const [onlineModalOpen, setOnlineModalOpen] = useState(false);
  const [customLinkModalOpen, setCustomLinkModalOpen] = useState(false);

  const handleContinue = () => {
    const eventType = watch('eventFormat');
    if (eventType === 'ONLINE') {
      setOnlineModalOpen(true);
    } else if (eventType === 'HYBRID') {
      router.push('/events/create/hybrid');
    } else {
      router.push('/events/create/add-address');
    }
  };

  return (
    <CreateEventLayout
      title='How do you want to host this event?'
      subTitle='Choose the format that suits your event.'
      footer={
        <Button
          data-testid='go-to-add-location-page'
          label='Continue'
          className='m-4'
          disabled={!fieldStates.eventFormat.isValid}
          onClick={handleContinue}
        />
      }
    >
      {EVENT_FORMAT_CARDS.map((card) => (
        <GradientBorderCard
          key={card.id}
          isSelected={watch('eventFormat') === card.id}
          onClick={() => setValue('eventFormat', card.id)}
          icon={card.icon}
          title={card.title}
          description={card.description}
        />
      ))}

      {/* Online Modal */}
      <Modal
        isOpen={onlineModalOpen}
        onClose={() => setOnlineModalOpen(false)}
        maxHeight='28%'
      >
        <div className='gap-2 p-4'>
          <H2 className='text-start'>Online events</H2>

          <a
            className='flex-row items-center justify-start gap-4 py-3 cursor-pointer'
            onClick={() => {
              setOnlineModalOpen(false);
              setCustomLinkModalOpen(true);
            }}
          >
            <div className='bg-accent-subtle dark:bg-accent-subtle size-10 rounded-full p-2'>
              <FaGlobe size={24} />
            </div>
            <P className='flex-1 flex items-center justify-center'>
              Use your own URL
            </P>
          </a>

          <Button
            label='Send link to attendees later'
            variant='outline'
            onClick={() => {
              setOnlineModalOpen(false);
              router.push('/events/create/choose-ticket-type');
            }}
          />
        </div>
      </Modal>

      {/* Custom Link Modal */}
      <Modal
        isOpen={customLinkModalOpen}
        onClose={() => {
          setCustomLinkModalOpen(false);
          setOnlineModalOpen(true);
        }}
        maxHeight='40%'
      >
        <div className='w-full justify-start p-4'>
          <div className='flex flex-row justify-start pb-4 items-center gap-2'>
            <button
              type='button'
              className='size-8 cursor-pointer'
              onClick={() => {
                setCustomLinkModalOpen(false);
                setOnlineModalOpen(true);
              }}
              aria-label='Back to online events modal'
            >
              <IoChevronBack
                size={24}
                color={colors.brand['60']}
                className='text-accent-moderate dark:text-accent-moderate'
              />
            </button>
            <P className='font-bold m-0'>Enter event URL</P>
          </div>
          <div className='gap-4'>
            <ControlledInput
              name='onlineUrl'
              control={control}
              label='Enter link (URL)'
            />
            <ControlledInput
              name='passcode'
              control={control}
              label='Link passcode (optional)'
            />

            <Button
              label='Save'
              className='mt-auto'
              disabled={!fieldStates.onlineUrl.isValid}
              onClick={() => {
                setCustomLinkModalOpen(false);
                router.push('/events/create/choose-ticket-type');
              }}
            />
          </div>
        </div>
      </Modal>
    </CreateEventLayout>
  );
}
