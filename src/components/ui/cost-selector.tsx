import React from 'react';
import { Control, Controller, FieldValues, UseFormSetValue } from 'react-hook-form';

import { CostOption } from './cost-option';
import { formatAmount, type CreateEventFormType } from '@/lib';

interface CostSelectorProps<T extends FieldValues> {
  control: Control<T>;
  setValue: UseFormSetValue<T>;
  name?: string;
  label?: string;
  costOptions?: number[];
  testID?: string;
  handleFieldBlur?: () => void;
  handleFieldUnBlur?: () => void;
}

export const CostSelector: React.FC<CostSelectorProps<CreateEventFormType>> = ({
  control,
  setValue,
  name = 'cost',
  handleFieldBlur,
  handleFieldUnBlur,
  label = 'Cost per request',
  costOptions = [0, 10000, 20000, 50000],
  testID = 'cost-input',
}) => {
  return (
    <div className="flex flex-col gap-2">
      <label htmlFor={testID} className="text-sm font-semibold">
        {label}
      </label>
      <Controller
        control={control}
        name={name as any}
        render={({ field }) => (
          <input
            id={testID}
            type="number"
            value={field.value ?? ''}
            onChange={(e) => {
              const val = Number(e.target.value);
              field.onChange(val);
              setValue('tickets.0.price', val);
            }}
            onBlur={handleFieldBlur}
            onFocus={handleFieldUnBlur}
            className="rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-hidden focus:ring-2 focus:ring-blue-500"
          />
        )}
      />

      <div className="flex gap-2 overflow-x-auto py-2">
        {costOptions.map((cost, index) => (
          <CostOption
            key={index}
            option={cost === 0 ? 'Free' : formatAmount(cost)}
            onPress={() => setValue(name as any, Number(cost))}
          />
        ))}
      </div>
    </div>
  );
};