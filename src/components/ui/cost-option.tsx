import React from 'react';

export type CostOptionProps = {
  option: string;
  onPress: () => void;
};

export const CostOption: React.FC<CostOptionProps> = ({ option, onPress }) => (
  <button
    type="button"
    onClick={onPress}
    className="rounded-full bg-accent-subtle-light px-4 py-2 dark:bg-accent-subtle-dark"
  >
    <span className="text-accent-bold-light dark:text-accent-bold-dark font-bold text-xs">
      {option}
    </span>
  </button>
);