import React from 'react';
import { MdRegularLabel, SmRegularLabel, Switch } from '@/components/ui';

type FeatureToggleProps = {
  title: string;
  subtitle?: string;
  checked: boolean;
  onChange: (checked: boolean) => void;
  accessibilityLabel: string;
  editText?: string;
  onEditPress?: () => void;
};

export const FeatureToggle: React.FC<FeatureToggleProps> = ({
  title,
  subtitle,
  checked,
  onChange,
  accessibilityLabel,
  editText,
  onEditPress,
}) => (
  <div className='flex flex-row items-center justify-center gap-x-4 rounded-md bg-bg-subtle-light px-4 py-3 dark:bg-bg-subtle-dark'>
    <div className='flex-1 gap-2.5 flex flex-col'>
      <MdRegularLabel>{title}</MdRegularLabel>

      {subtitle && !editText && (
        <SmRegularLabel className='text-fg-muted-light dark:text-fg-muted-dark'>
          {subtitle}
        </SmRegularLabel>
      )}

      {editText && (
        <button
          type='button'
          onClick={onEditPress}
          className='text-fg-muted-light underline dark:text-fg-muted-dark'
        >
          <SmRegularLabel>{editText}</SmRegularLabel>
        </button>
      )}
    </div>

    <Switch
      checked={checked}
      onChange={(e) => onChange((e.target as HTMLInputElement).checked)}
      id={accessibilityLabel.toLowerCase().replace(/\s+/g, '-')}
      aria-label={accessibilityLabel}
    />
  </div>
);
