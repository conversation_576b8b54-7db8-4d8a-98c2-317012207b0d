import React, { useEffect } from 'react';

type ModalProps = {
  isOpen: boolean;
  title?: string;
  onClose: () => void;
  children: React.ReactNode;
  maxHeight?: string;
};

export const Modal: React.FC<ModalProps> = ({ isOpen, title, onClose, children }) => {
  // Close modal on ESC key
  useEffect(() => {
    const onKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };
    window.addEventListener('keydown', onKeyDown);
    return () => window.removeEventListener('keydown', onKeyDown);
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <>
      <div className="modal-backdrop" onClick={onClose} />

      <div
        className="modal-container"
        role="dialog"
        aria-modal="true"
        aria-labelledby="modal-title"
        onClick={e => e.stopPropagation()} // Prevent closing modal when clicking inside
      >
        {title && <h2 id="modal-title" className="modal-title">{title}</h2>}
        <button
          aria-label="Close modal"
          className="modal-close-btn"
          onClick={onClose}
        >
          &times;
        </button>

        <div className="modal-content">{children}</div>
      </div>

      <style>{`
        .modal-backdrop {
          position: fixed;
          inset: 0;
          background-color: rgba(0,0,0,0.4);
          animation: fadeIn 200ms ease forwards;
          z-index: 999;
        }
        .modal-container {
          position: fixed;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          background: white;
          border-radius: 8px;
          max-width: 90vw;
          max-height: 90vh;
          overflow-y: auto;
          padding: 24px 32px;
          box-shadow: 0 4px 20px rgba(0,0,0,0.3);
          animation: scaleIn 200ms ease forwards;
          z-index: 1000;
        }
        .modal-title {
          margin: 0 0 16px 0;
          font-size: 1.5rem;
          font-weight: 600;
        }
        .modal-close-btn {
          position: absolute;
          top: 12px;
          right: 12px;
          background: transparent;
          border: none;
          font-size: 1.5rem;
          cursor: pointer;
          line-height: 1;
        }
        .modal-content {
          font-size: 1rem;
        }

        @keyframes fadeIn {
          from { opacity: 0 }
          to { opacity: 1 }
        }
        @keyframes scaleIn {
          from {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0.9);
          }
          to {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
          }
        }
      `}</style>
    </>
  );
};