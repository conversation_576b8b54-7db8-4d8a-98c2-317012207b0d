'use client';
import { useJs<PERSON>pi<PERSON>oader } from '@react-google-maps/api';
import { useEffect, useState } from 'react';
import GooglePlacesAutocomplete, {
  geocodeByAddress,
  getLatLng,
} from 'react-google-places-autocomplete';
import type { FieldValues, UseFormReturn } from 'react-hook-form';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/dialogs';

import { env } from '@/env.mjs';
import { Input } from '@/components/inputs/input';

interface IProps<T extends FieldValues> {
  formKey: string;
  onChange: (value: string) => void;
  // onLatLngChange: (lat: number, lng: number) => void;
  value: string | undefined;
  useDefault?: boolean;
}

type Library = 'places';
const libraries: Library[] = ['places'];

export default function GooglePlacesAutocompleteButton<T extends FieldValues>({
  value,
  onChange,
  // onLatLngChange,
  useDefault,
  formKey,
}: IProps<T>) {
  const [open, setOpen] = useState(false);
  const [addressValue, setAddressValue] = useState(value ?? '');

  const { isLoaded } = useJsApiLoader({
    id: 'google-map-autocomplete-strict',
    googleMapsApiKey: env.NEXT_PUBLIC_GOOGLE_MAP_API_KEY,
    libraries,
  });

  const handleAutoComplete = (address: string) => {
    if (!address || useDefault) return;
    setAddressValue(address);
    // onChange(address);
    geocodeByAddress(address)
      .then((results) => getLatLng(results[0]!))
      .then(async ({ lat, lng }) => {
        try {
          const response = await fetch(
            `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${env.NEXT_PUBLIC_GOOGLE_MAP_API_KEY}`
          );
          const data = await response.json();
          // const formattedAddress =
          //   data.results[0]?.formatted_address || 'Unknown';

          // console.log('data-----', data.results[0]);
          data.results[0] && onChange(data.results[0]);
        } catch (error) {
          console.error('Error fetching address:', error);
        }
        // form.setValue(
        //   `${formKey}.formatted` as Path<T>,
        //   address as PathValue<T, Path<T>>
        // );
        // onLatLngChange(lat, lng);
        // form.setValue(
        //   `${formKey}.latitude` as Path<T>,
        //   lat as PathValue<T, Path<T>>
        // );
        // form.setValue(
        //   `${formKey}.longitude` as Path<T>,
        //   lng as PathValue<T, Path<T>>
        // );
      })
      .catch((err) => console.error('Error', err))
      .finally(() => setOpen(false));
  };

  useEffect(() => {
    if (value) {
      setAddressValue(value);
    }
  }, [value]);

  return (
    <>
      <Dialog onOpenChange={setOpen} open={open}>
        <DialogTrigger asChild>
          <div className='flex flex-1 md:w-[410px]'>
            <Input
              placeholder='Enter Event Location'
              className=''
              value={addressValue}
            />
          </div>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Enter Address</DialogTitle>
            <DialogDescription>
              Start typing to find an address
            </DialogDescription>
          </DialogHeader>
          <GooglePlacesAutocomplete
            apiKey={env.NEXT_PUBLIC_GOOGLE_MAP_API_KEY}
            selectProps={{
              defaultInputValue: addressValue,
              onChange: (e) => handleAutoComplete(e!.label),
              classNames: {
                control: (state) =>
                  state.isFocused ? 'border-red-600' : 'border-grey-300',
                input: () => 'text-bold',
              },
              styles: {
                input: (provided) => ({
                  ...provided,
                  outline: '0px solid',
                }),
              },
            }}
          />
          <DialogFooter></DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
