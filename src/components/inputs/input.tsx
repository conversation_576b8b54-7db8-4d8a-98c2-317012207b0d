import * as React from 'react';
import { cn } from '@/lib/utils';

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  suffix?: React.ReactNode;
  prefixIcon?: React.ReactNode;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ suffix, prefixIcon, className, type, ...props }, ref) => {

    const handleWheel = (e: React.WheelEvent<HTMLInputElement>) => {
      // Prevent the default behavior of incrementing/decrementing on scroll
      e.currentTarget.blur();
    };

    return (
      <div className='relative flex gap-2 items-center w-full'>
        {prefixIcon && (
          <div className='absolute left-3 cursor-pointer'>{prefixIcon}</div>
        )}
        <input
          type={type}
          onWheel={handleWheel}
          className={cn(
            `flex h-10 w-full rounded-lg border border-gray-300 bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus:outline-hidden focus-visible:outline-hidden focus:ring-0 disabled:cursor-not-allowed disabled:opacity-50 ${prefixIcon ? ' pl-[60px]' : ''}`,
            className
          )}
          ref={ref}
          {...props}
        />
        {suffix && (
          <div className='absolute right-2 cursor-pointer'>{suffix}</div>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input';

export { Input };
