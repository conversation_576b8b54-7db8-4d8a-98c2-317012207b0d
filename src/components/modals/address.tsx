'use client';
import { Input } from '@/components/inputs/input';
import React, { useState } from 'react';
import GooglePlacesAutocomplete, {
  geocodeByPlaceId,
} from 'react-google-places-autocomplete';
import 'tailwindcss/tailwind.css';

const AddressAutocompleteModal: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [selectedAddress, setSelectedAddress] = useState<string>('');

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  return (
    <>
      <div className='flex flex-1 md:w-[410px]'>
        <Input
          onClick={openModal}
          // disabled
          placeholder='Enter Event Location'
          className=''
        />
      </div>

      {isModalOpen && (
        <div className='fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50'>
          <div className='bg-white rounded-lg w-full max-w-md mx-4 sm:mx-auto p-6'>
            <div className='flex justify-between items-center mb-4'>
              <h2 className='text-xl font-semibold'>Select Address</h2>
              <button
                onClick={closeModal}
                className='text-gray-600 hover:text-gray-800 focus:outline-hidden'
              >
                ✕
              </button>
            </div>

            <div className='mb-4'>
              <GooglePlacesAutocomplete
                apiKey={process.env.NEXT_PUBLIC_GOOGLE_MAP_API_KEY}
                selectProps={{
                  placeholder: 'Enter an address',
                  onChange: async (value) => {
                    if (value?.value?.place_id) {
                      const results = await geocodeByPlaceId(
                        value.value.place_id
                      );
                      const address = results[0].formatted_address;
                      setSelectedAddress(address);
                    }
                  },
                  className: 'w-full',
                  styles: {
                    container: (provided) => ({
                      ...provided,
                      width: '100%',
                    }),
                    input: (provided) => ({
                      ...provided,
                      padding: '0.75rem',
                      border: '1px solid #E5E7EB',
                      borderRadius: '0.5rem',
                      outline: 'none',
                      boxShadow: 'none',
                      '&:focus': {
                        borderColor: '#3B82F6',
                      },
                    }),
                  },
                }}
              />
            </div>

            {selectedAddress && (
              <div className='mt-4'>
                <h3 className='text-lg font-semibold'>Selected Address</h3>
                <p className='text-sm text-gray-800'>{selectedAddress}</p>
              </div>
            )}

            <div className='flex justify-end mt-4'>
              <button
                onClick={closeModal}
                className='bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition'
              >
                Confirm Address
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default AddressAutocompleteModal;
